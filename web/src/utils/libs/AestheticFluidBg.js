/**
 * AestheticFluidBg - 美学流体背景效果
 * 创建动态的流体背景动画效果
 */
export class AestheticFluidBg {
  constructor(options = {}) {
    this.options = {
      dom: options.dom || 'body',
      colors: options.colors || ['#D1ADFF', '#98D69B', '#FAE390', '#FFACD8', '#7DD5FF', '#D1ADFF'],
      seed: options.seed || 1000,
      loop: options.loop !== false,
      speed: options.speed || 0.5,
      intensity: options.intensity || 0.8,
      ...options
    }
    
    this.canvas = null
    this.ctx = null
    this.animationId = null
    this.particles = []
    this.time = 0
    
    this.init()
  }
  
  init() {
    this.createCanvas()
    this.createParticles()
    this.bindEvents()
    if (this.options.loop) {
      this.animate()
    }
  }
  
  createCanvas() {
    const container = typeof this.options.dom === 'string' 
      ? document.getElementById(this.options.dom) || document.querySelector(this.options.dom)
      : this.options.dom
      
    if (!container) {
      console.error('AestheticFluidBg: Container not found')
      return
    }
    
    this.canvas = document.createElement('canvas')
    this.canvas.style.position = 'absolute'
    this.canvas.style.top = '0'
    this.canvas.style.left = '0'
    this.canvas.style.width = '100%'
    this.canvas.style.height = '100%'
    this.canvas.style.zIndex = '-1'
    this.canvas.style.pointerEvents = 'none'
    
    container.style.position = 'relative'
    container.appendChild(this.canvas)
    
    this.ctx = this.canvas.getContext('2d')
    this.resize()
  }
  
  createParticles() {
    const particleCount = Math.floor((this.canvas.width * this.canvas.height) / 15000)
    this.particles = []
    
    for (let i = 0; i < particleCount; i++) {
      this.particles.push({
        x: Math.random() * this.canvas.width,
        y: Math.random() * this.canvas.height,
        vx: (Math.random() - 0.5) * this.options.speed,
        vy: (Math.random() - 0.5) * this.options.speed,
        radius: Math.random() * 100 + 50,
        color: this.options.colors[Math.floor(Math.random() * this.options.colors.length)],
        opacity: Math.random() * 0.5 + 0.1,
        phase: Math.random() * Math.PI * 2
      })
    }
  }
  
  resize() {
    if (!this.canvas) return
    
    const rect = this.canvas.parentElement.getBoundingClientRect()
    this.canvas.width = rect.width
    this.canvas.height = rect.height
    
    if (this.particles.length === 0) {
      this.createParticles()
    }
  }
  
  bindEvents() {
    window.addEventListener('resize', () => this.resize())
  }
  
  animate() {
    if (!this.ctx || !this.canvas) return
    
    this.time += 0.01
    
    // 清除画布
    this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height)
    
    // 创建渐变背景
    const gradient = this.ctx.createLinearGradient(0, 0, this.canvas.width, this.canvas.height)
    gradient.addColorStop(0, 'rgba(255, 255, 255, 0.02)')
    gradient.addColorStop(1, 'rgba(255, 255, 255, 0.05)')
    this.ctx.fillStyle = gradient
    this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height)
    
    // 绘制粒子
    this.particles.forEach((particle, index) => {
      // 更新粒子位置
      particle.x += particle.vx + Math.sin(this.time + particle.phase) * 0.5
      particle.y += particle.vy + Math.cos(this.time + particle.phase) * 0.5
      
      // 边界检测
      if (particle.x < -particle.radius) particle.x = this.canvas.width + particle.radius
      if (particle.x > this.canvas.width + particle.radius) particle.x = -particle.radius
      if (particle.y < -particle.radius) particle.y = this.canvas.height + particle.radius
      if (particle.y > this.canvas.height + particle.radius) particle.y = -particle.radius
      
      // 动态透明度
      particle.opacity = (Math.sin(this.time * 2 + particle.phase) + 1) * 0.15 + 0.1
      
      // 绘制粒子
      this.drawParticle(particle)
    })
    
    // 绘制连接线
    this.drawConnections()
    
    if (this.options.loop) {
      this.animationId = requestAnimationFrame(() => this.animate())
    }
  }
  
  drawParticle(particle) {
    const gradient = this.ctx.createRadialGradient(
      particle.x, particle.y, 0,
      particle.x, particle.y, particle.radius
    )
    
    const color = this.hexToRgb(particle.color)
    gradient.addColorStop(0, `rgba(${color.r}, ${color.g}, ${color.b}, ${particle.opacity})`)
    gradient.addColorStop(0.4, `rgba(${color.r}, ${color.g}, ${color.b}, ${particle.opacity * 0.6})`)
    gradient.addColorStop(1, `rgba(${color.r}, ${color.g}, ${color.b}, 0)`)
    
    this.ctx.fillStyle = gradient
    this.ctx.beginPath()
    this.ctx.arc(particle.x, particle.y, particle.radius, 0, Math.PI * 2)
    this.ctx.fill()
  }
  
  drawConnections() {
    for (let i = 0; i < this.particles.length; i++) {
      for (let j = i + 1; j < this.particles.length; j++) {
        const dx = this.particles[i].x - this.particles[j].x
        const dy = this.particles[i].y - this.particles[j].y
        const distance = Math.sqrt(dx * dx + dy * dy)
        
        if (distance < 200) {
          const opacity = (200 - distance) / 200 * 0.1
          this.ctx.strokeStyle = `rgba(255, 255, 255, ${opacity})`
          this.ctx.lineWidth = 1
          this.ctx.beginPath()
          this.ctx.moveTo(this.particles[i].x, this.particles[i].y)
          this.ctx.lineTo(this.particles[j].x, this.particles[j].y)
          this.ctx.stroke()
        }
      }
    }
  }
  
  hexToRgb(hex) {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex)
    return result ? {
      r: parseInt(result[1], 16),
      g: parseInt(result[2], 16),
      b: parseInt(result[3], 16)
    } : { r: 255, g: 255, b: 255 }
  }
  
  destroy() {
    if (this.animationId) {
      cancelAnimationFrame(this.animationId)
      this.animationId = null
    }
    
    if (this.canvas && this.canvas.parentElement) {
      this.canvas.parentElement.removeChild(this.canvas)
    }
    
    window.removeEventListener('resize', () => this.resize())
  }
  
  pause() {
    if (this.animationId) {
      cancelAnimationFrame(this.animationId)
      this.animationId = null
    }
  }
  
  resume() {
    if (!this.animationId && this.options.loop) {
      this.animate()
    }
  }
}
